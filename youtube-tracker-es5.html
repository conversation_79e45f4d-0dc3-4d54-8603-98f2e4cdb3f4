<script>
(function() {
  // Prevent re-injecting the API
  if (!window._ytApiInjected) {
    window._ytApiInjected = true;

    var tag = document.createElement('script');
    tag.src = "https://www.youtube.com/iframe_api";
    tag.async = true;
    document.head.appendChild(tag);
  }

  // Store a queue of actions to run when API is ready
  window._ytPlayerInitQueue = window._ytPlayerInitQueue || [];

  // Global function required by YouTube API
  window.onYouTubeIframeAPIReady = function() {
    window._ytApiReady = true;
    window._ytPlayerInitQueue.forEach(function(cb) { cb(); });
    window._ytPlayerInitQueue = [];
  };

  function initYouTubePlayers() {
    var iframes = document.querySelectorAll('.videobg-aspect iframe');

    iframes.forEach(function(iframe) {
      iframe.setAttribute('data-yt-tracked', 'true');

      var id = iframe.id || 'yt-' + Math.floor(Math.random() * 100000);
      iframe.id = id;

      new YT.Player(id, {
        events: {
          onStateChange: function(event) {
            var state = event.data;
            var player = event.target;
            var videoData = player.getVideoData();
            var videoUrl = player.getVideoUrl();

            var eventMap = {};
            eventMap[YT.PlayerState.PLAYING] = 'youtube_play';
            eventMap[YT.PlayerState.PAUSED] = 'youtube_pause';
            eventMap[YT.PlayerState.ENDED] = 'youtube_complete';

            var eventName = eventMap[state];
            if (eventName) {
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                event: eventName,
                video_title: videoData.title,
                video_url: videoUrl
              });
            }
          }
        }
      });
    });
  }


  
  function observeNewIframes() {
    var observer = new MutationObserver(function() {
      if (window.YT && window.YT.Player) {
        initYouTubePlayers();
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  }

  // Push initialization function to run once API is ready
  function scheduleInit() {
    if (window._ytApiReady) {
      initYouTubePlayers();
    } else {
      window._ytPlayerInitQueue.push(initYouTubePlayers);
    }
  }

  // Add click listener for toggle-media
  function addToggleMediaListener() {
    var toggleElements = document.querySelectorAll('.toggle-media');
    toggleElements.forEach(function(element) {
      element.addEventListener('click', function() {
        // Run initYouTubePlayers when toggle-media is clicked
        if (window.YT && window.YT.Player) {
          initYouTubePlayers();
        } else {
          window._ytPlayerInitQueue.push(initYouTubePlayers);
        }
      });
    });
  }

  // Observe DOM changes now
  observeNewIframes();
  scheduleInit();
  addToggleMediaListener();

})();
</script>