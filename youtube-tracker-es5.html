<script>
(function() {
  if (window._ytTrackerInitialized) return;
  window._ytTrackerInitialized = true;

  if (!window.YT || !window.YT.Player) {
    setTimeout(function() {
      var script = document.createElement('script');
      script.src = "https://www.youtube.com/iframe_api";
      script.async = true;
      document.head.appendChild(script);
    }, 0);
  }

  var ytReadyCallbacks = [];

  function onYouTubeReady(callback) {
    if (window.YT && window.YT.Player) {
      callback();
    } else {
      ytReadyCallbacks.push(callback);
    }
  }

  window.onYouTubeIframeAPIReady = function() {
    while (ytReadyCallbacks.length) {
      ytReadyCallbacks.shift()();
    }
  };

  function initPlayers() {
    var iframes = document.querySelectorAll('iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])');

    iframes.forEach(function(iframe) {
      if (iframe.getAttribute('data-yt-tracked')) return;
      iframe.setAttribute('data-yt-tracked', 'true');

      if (!iframe.id) iframe.id = 'yt-' + Math.floor(Math.random() * 100000);
      var id = iframe.id;

      if (iframe.complete) {
        attachPlayer(id);
      } else {
        iframe.addEventListener('load', function onLoad() {
          iframe.removeEventListener('load', onLoad);
          attachPlayer(id);
        });
      }
    });
  }

  function attachPlayer(id) {
    if (!window.YT || !YT.Player) return;

    new YT.Player(id, {
      events: {
        onStateChange: function(event) {
          const state = event.data;
          const player = event.target;
          const videoData = player.getVideoData();
          const videoUrl = player.getVideoUrl();

          const stateMap = {
            0: 'youtube_complete',
            1: 'youtube_play',
            2: 'youtube_pause'
          };

          const eventName = stateMap[state];
          if (eventName) {
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              event: eventName,
              video_title: videoData.title,
              video_url: videoUrl
            });
          }
        }
      }
    });
  }

  function observeDOM() {
    const observer = new MutationObserver(() => {
      onYouTubeReady(initPlayers);
    });
    observer.observe(document.body, { childList: true, subtree: true });
  }

  function scanInitialIframes() {
    let attempts = 0;
    const maxAttempts = 15;
    const interval = setInterval(() => {
      if (++attempts > maxAttempts) {
        clearInterval(interval);
      } else {
        initPlayers();
      }
    }, 300);
  }

  onYouTubeReady(() => {
    initPlayers();
    scanInitialIframes();
  });

  observeDOM();
})();
</script>
