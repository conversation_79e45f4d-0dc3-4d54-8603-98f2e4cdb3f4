<script>
(function() {
  console.log('[YT Tracker] Script started');
  var iframeSelector = 'iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])';
  console.log('[YT Tracker] Using selector:', iframeSelector);

  function loadYTApi(callback) {
    console.log('[YT Tracker] loadYTApi called');
    if (window.YT && window.YT.Player) {
      console.log('[YT Tracker] YouTube API already loaded, calling callback immediately');
      callback();
    } else {
      console.log('[YT Tracker] YouTube API not loaded, loading script...');
      var tag = document.createElement('script');
      tag.src = "https://www.youtube.com/iframe_api";
      document.head.appendChild(tag);
      console.log('[YT Tracker] YouTube API script added to head');

      window.onYouTubeIframeAPIReady = function() {
        console.log('[YT Tracker] YouTube API ready callback fired');
        callback();
      };
    }
  }

  function trackFirstYouTubeIframe() {
    console.log('[YT Tracker] trackFirstYouTubeIframe called');
    console.log('[YT Tracker] Looking for iframes with selector:', iframeSelector);

    var allIframes = document.querySelectorAll('iframe');
    console.log('[YT Tracker] Total iframes found:', allIframes.length);

    var youtubeIframes = document.querySelectorAll('iframe[src*="youtube.com/embed/"]');
    console.log('[YT Tracker] YouTube iframes found:', youtubeIframes.length);

    var iframe = document.querySelector(iframeSelector);
    console.log('[YT Tracker] Target iframe found:', !!iframe);

    if (!iframe) {
      console.log('[YT Tracker] No iframe found, exiting');
      return;
    }

    console.log('[YT Tracker] Iframe src:', iframe.src);
    console.log('[YT Tracker] Iframe id before:', iframe.id);

    if (!iframe.id) {
      iframe.id = 'yt-' + Math.floor(Math.random() * 100000);
      console.log('[YT Tracker] Generated new iframe id:', iframe.id);
    }

    iframe.setAttribute('data-yt-tracked', 'true');
    console.log('[YT Tracker] Set data-yt-tracked attribute');

    console.log('[YT Tracker] Creating YT.Player for id:', iframe.id);
    try {
      new YT.Player(iframe.id, {
        events: {
          onStateChange: function(event) {
            console.log('[YT Tracker] State change event fired');
            var state = event.data;
            var player = event.target;
            console.log('[YT Tracker] State:', state);

            try {
              var videoData = player.getVideoData();
              var videoUrl = player.getVideoUrl();
              console.log('[YT Tracker] Video data:', videoData);
              console.log('[YT Tracker] Video URL:', videoUrl);

              var stateMap = {
                0: 'youtube_complete',
                1: 'youtube_play',
                2: 'youtube_pause'
              };

              var eventName = stateMap[state];
              console.log('[YT Tracker] Event name:', eventName);

              if (eventName) {
                window.dataLayer = window.dataLayer || [];
                var eventData = {
                  event: eventName,
                  video_title: videoData.title,
                  video_url: videoUrl
                };
                console.log('[YT Tracker] Pushing to dataLayer:', eventData);
                window.dataLayer.push(eventData);
              }
            } catch (error) {
              console.error('[YT Tracker] Error in state change handler:', error);
            }
          }
        }
      });
      console.log('[YT Tracker] YT.Player created successfully');
    } catch (error) {
      console.error('[YT Tracker] Error creating YT.Player:', error);
    }
  }

  // Wait a short time for the popup/iframe to render
  console.log('[YT Tracker] Starting API load process');
  loadYTApi(function() {
    console.log('[YT Tracker] API loaded, setting timeout for tracking');
    setTimeout(function() {
      console.log('[YT Tracker] Timeout fired, calling trackFirstYouTubeIframe');
      trackFirstYouTubeIframe();
    }, 1200); // adjust delay if needed
  });
})();
</script>