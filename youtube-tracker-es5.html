<script>
(function() {
  // Prevent re-injecting the API
  if (!window._ytApiInjected) {
    window._ytApiInjected = true;

    var tag = document.createElement('script');
    tag.src = "https://www.youtube.com/iframe_api";
    tag.async = true;
    document.head.appendChild(tag);
  }

  // Store a queue of actions to run when API is ready
  window._ytPlayerInitQueue = window._ytPlayerInitQueue || [];

  // Global function required by YouTube API
  window.onYouTubeIframeAPIReady = function() {
    window._ytApiReady = true;
    window._ytPlayerInitQueue.forEach(function(cb) { cb(); });
    window._ytPlayerInitQueue = [];
  };

  function initYouTubePlayers() {
    const iframes = document.querySelectorAll('iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])');

    iframes.forEach(function(iframe) {
      iframe.setAttribute('data-yt-tracked', 'true');

      const id = iframe.id || 'yt-' + Math.floor(Math.random() * 100000);
      iframe.id = id;

      new YT.Player(id, {
        events: {
          onStateChange: function(event) {
            const state = event.data;
            const player = event.target;
            const videoData = player.getVideoData();
            const videoUrl = player.getVideoUrl();

            const eventMap = {
              [YT.PlayerState.PLAYING]: 'youtube_play',
              [YT.PlayerState.PAUSED]: 'youtube_pause',
              [YT.PlayerState.ENDED]: 'youtube_complete'
            };

            const eventName = eventMap[state];
            if (eventName) {
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                event: eventName,
                video_title: videoData.title,
                video_url: videoUrl
              });
            }
          }
        }
      });
    });
  }

  function observeNewIframes() {
    const observer = new MutationObserver(() => {
      if (window.YT && window.YT.Player) {
        initYouTubePlayers();
      }
    });
    observer.observe(document.body, { childList: true, subtree: true });
  }

  // Push initialization function to run once API is ready
  function scheduleInit() {
    if (window._ytApiReady) {
      initYouTubePlayers();
    } else {
      window._ytPlayerInitQueue.push(initYouTubePlayers);
    }
  }

  // Observe DOM changes now
  observeNewIframes();
  scheduleInit();

})();
</script>