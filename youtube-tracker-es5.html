<script>
(function() {
  // Prevent multiple inits
  if (window._ytTrackerInitialized) return;
  window._ytTrackerInitialized = true;

  // Dynamically load YouTube API with GTM sandbox escape
  if (!window.YT) {
    setTimeout(function() {
      var script = document.createElement('script');
      script.src = "https://www.youtube.com/iframe_api";
      script.async = true;
      document.head.appendChild(script);
    }, 0);
  }

  // Poll for YT API readiness instead of using onYouTubeIframeAPIReady
  function waitForYT() {
    if (window.YT && window.YT.Player) {
      setupTracking();
    } else {
      setTimeout(waitForYT, 250);
    }
  }

  function setupTracking() {
    // Observe DOM changes for new YouTube iframes
    const observer = new MutationObserver(() => {
      initPlayers();
    });
    observer.observe(document.body, { childList: true, subtree: true });

    // Initial run in case iframe is already present
    initPlayers();
  }

  function initPlayers() {
    const iframes = document.querySelectorAll('iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])');

    iframes.forEach((iframe) => {
      iframe.setAttribute('data-yt-tracked', 'true');
      if (!iframe.id) {
        iframe.id = 'yt-' + Math.floor(Math.random() * 100000);
      }

      new YT.Player(iframe.id, {
        events: {
          onStateChange: function(event) {
            const state = event.data;
            const videoData = event.target.getVideoData();
            const videoUrl = event.target.getVideoUrl();

            const stateMap = {
              0: 'youtube_complete',
              1: 'youtube_play',
              2: 'youtube_pause'
            };

            const eventName = stateMap[state];
            if (eventName) {
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                event: eventName,
                video_title: videoData.title,
                video_url: videoUrl
              });
            }
          }
        }
      });
    });
  }

  waitForYT();
})();
</script>
