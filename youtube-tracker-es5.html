<script>
(function() {
  const iframeSelector = 'iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])';

  function loadYTApi(callback) {
    if (window.YT && window.YT.Player) {
      callback();
    } else {
      var tag = document.createElement('script');
      tag.src = "https://www.youtube.com/iframe_api";
      document.head.appendChild(tag);

      window.onYouTubeIframeAPIReady = function() {
        callback();
      };
    }
  }

  function trackFirstYouTubeIframe() {
    const iframe = document.querySelector(iframeSelector);
    if (!iframe) return;

    if (!iframe.id) iframe.id = 'yt-' + Math.floor(Math.random() * 100000);
    iframe.setAttribute('data-yt-tracked', 'true');

    new YT.Player(iframe.id, {
      events: {
        onStateChange: function(event) {
          const state = event.data;
          const player = event.target;
          const videoData = player.getVideoData();
          const videoUrl = player.getVideoUrl();

          const stateMap = {
            0: 'youtube_complete',
            1: 'youtube_play',
            2: 'youtube_pause'
          };

          const eventName = stateMap[state];
          if (eventName) {
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              event: eventName,
              video_title: videoData.title,
              video_url: videoUrl
            });
          }
        }
      }
    });
  }

  // Wait a short time for the popup/iframe to render
  loadYTApi(function() {
    setTimeout(trackFirstYouTubeIframe, 300); // adjust delay if needed
  });
})();
</script>