<script>
(function() {
  if (window._ytTrackerInitialized) return;
  window._ytTrackerInitialized = true;

  // Load YouTube API with sandbox escape
  if (!window.YT || !window.YT.Player) {
    setTimeout(function() {
      var script = document.createElement('script');
      script.src = "https://www.youtube.com/iframe_api";
      script.async = true;
      document.head.appendChild(script);
    }, 0);
  }

  const ytReadyCallbacks = [];

  function onYouTubeReady(callback) {
    if (window.YT && window.YT.Player) {
      callback();
    } else {
      ytReadyCallbacks.push(callback);
    }
  }

  window.onYouTubeIframeAPIReady = function() {
    while (ytReadyCallbacks.length) {
      ytReadyCallbacks.shift()();
    }
  };

  function initPlayers() {
    const iframes = document.querySelectorAll('iframe[src*="youtube.com/embed/"]:not([data-yt-tracked])');

    iframes.forEach((iframe) => {
      if (iframe.dataset.ytTracked) return;

      iframe.dataset.ytTracked = "true";
      if (!iframe.id) iframe.id = 'yt-' + Math.floor(Math.random() * 100000);

      new YT.Player(iframe.id, {
        events: {
          onStateChange: function(event) {
            const state = event.data;
            const player = event.target;
            const videoData = player.getVideoData();
            const videoUrl = player.getVideoUrl();

            const stateMap = {
              0: 'youtube_complete',
              1: 'youtube_play',
              2: 'youtube_pause'
            };

            const eventName = stateMap[state];
            if (eventName) {
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                event: eventName,
                video_title: videoData.title,
                video_url: videoUrl
              });
            }
          }
        }
      });
    });
  }

  function observeDOM() {
    const observer = new MutationObserver(() => {
      onYouTubeReady(initPlayers);
    });
    observer.observe(document.body, { childList: true, subtree: true });
  }

  // Also handle iframes that were already in the DOM (like the first one)
  onYouTubeReady(initPlayers);

  // Start watching for future iframes
  observeDOM();
})();
</script>
