<style>
.grid-container {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: 22px 22px;
}

.grid-item {
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  position: relative;
  overflow: visible;
}

.grid-item::before {
  content: '';
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 120%;
  height: 120%;
  background-image: var(--hover-image);
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  opacity: 1;
  transform: scale(1);
  pointer-events: none;
  z-index: 10;
  display: none;
}

.grid-item:hover::before {
  display: block;
}

.grid-item:hover {
  transform: translateZ(0);
  z-index: 20;
}

.item-1 {
  grid-column: 5 / 13;
  grid-row: 4 / 7;
  --hover-image: url('meow.png');
}

.item-3 {
  grid-column: 14 / 17;
  grid-row: 1 / 4;
  --hover-image: url('meow.png');
}

.item-6 {
  grid-column: 1 / 5;
  grid-row: 4 / 8;
  --hover-image: url('meow.png');
}

.item-7 {
  grid-column: 1 / 8;
  grid-row: 8 / 10;
  --hover-image: url('meow.png');
}

.item-8 {
  grid-column: 5 / 7;
  grid-row: 7 / 8;
  --hover-image: url('meow.png');
}

.item-9 {
  grid-column: 7 / 9;
  grid-row: 7 / 8;
  --hover-image: url('meow.png');
}

.item-10 {
  grid-column: 9 / 11;
  grid-row: 7 / 8;
  --hover-image: url('meow.png');
}

.item-11 {
  grid-column: 11 / 13;
  grid-row: 7 / 8;
  --hover-image: url('meow.png');
}

.item-14 {
  grid-column: 8 / 13;
  grid-row: 8 / 10;
  --hover-image: url('meow.png');
}

.item-15 {
  grid-column: 13 / 17;
  grid-row: 7 / 10;
  --hover-image: url('meow.png');
}

.item-16 {
  grid-column: 13 / 17;
  grid-row: 4 / 7;
  --hover-image: url('meow.png');
}

.item-17 {
  grid-column: 7 / 14;
  grid-row: 1 / 4;
  --hover-image: url('meow.png');
}

.item-18 {
  grid-column: 1 / 7;
  grid-row: 1 / 3;
  --hover-image: url('meow.png');
}

.item-19 {
  grid-column: 1 / 7;
  grid-row: 3 / 4;
  --hover-image: url('meow.png');
}


/* custom overrides */

.grid-item  {
    min-height: 7vh;
    box-sizing: border-box;
}

.grid-container {
    max-width: 1280px;
    margin: 5% auto;
}

body {
  background: url('grad.jpeg');
  background-size: cover;
}

</style>





<style>

.glass-button {
  position: relative;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.glass-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Additional demo buttons */
.button-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding: 500px 0;
}

.glass-button.secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
}

</style>

<div class="grid-container">
  <div class="grid-item glass-button item-1">
    The Last BlockBender
  </div>
  <div class="grid-item glass-button item-3">
    How to Bend
  </div>
  <div class="grid-item glass-button item-6">
    Wiki
  </div>
  <div class="grid-item glass-button item-7">
    Discord
  </div>
  <div class="grid-item glass-button item-8">
    Instagram
  </div>
  <div class="grid-item glass-button item-9">
    Youtube
  </div>
  <div class="grid-item glass-button item-10">
    Tiktok
  </div>
  <div class="grid-item glass-button item-11">
    Social 4
  </div>
  <div class="grid-item glass-button item-14">
    Vote
  </div>
  <div class="grid-item glass-button item-15">
    Store
  </div>
  <div class="grid-item glass-button item-16">
    FAQ
  </div>
  <div class="grid-item glass-button item-17">
    How to Join
  </div>
  <div class="grid-item glass-button item-18">
    Java Edition
  </div>
  <div class="grid-item glass-button item-19">
    Bedrock Edition
  </div>
</div>
